# AI Configuration
# The system now uses intelligent fallback: OpenAI -> Ollama -> Intelligent Truncation
# Set AI_SERVICE to override default behavior (optional)
AI_SERVICE=openai

# OpenAI Configuration (Primary - Recommended)
# Get your API key from: https://platform.openai.com/
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-3.5-turbo

# Ollama Configuration (Fallback - Local)
# Requires Ollama installation and sufficient memory
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama3

# Flask Configuration
FLASK_ENV=development
FLASK_DEBUG=True
